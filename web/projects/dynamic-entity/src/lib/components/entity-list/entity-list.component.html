<div class="entities-grid">
  <div class="entity-card" *ngFor="let entity of translatedEntities" (click)="onEntitySelect(entity)">
    <div class="entity-icon">
      <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </div>
    <div class="entity-info">
      <h3 class="entity-name">
        {{ entity.name }}
      </h3>
      <p class="entity-fields-count">{{ entity.customFieldsCount }} {{ translations.customFieldsCount }}</p>
    </div>
    <div class="entity-arrow">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </div>
  </div>
</div>
