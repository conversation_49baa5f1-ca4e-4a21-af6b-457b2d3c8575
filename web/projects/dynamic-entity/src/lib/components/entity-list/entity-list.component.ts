import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Entity } from '../../interfaces/dynamic-entity.interfaces';
import { SupportedLanguage, LanguageTranslations, LANGUAGE_TRANSLATIONS } from '../../constants/language.constants';
import { SAMPLE_ENTITIES } from '../../constants/field-types.constants';

@Component({
  selector: 'lib-entity-list',
  templateUrl: './entity-list.component.html',
  styleUrls: ['./entity-list.component.scss']
})
export class EntityListComponent {
  @Input() entities: Entity[] = SAMPLE_ENTITIES;
  @Input() language: SupportedLanguage = 'en';
  @Output() entitySelected = new EventEmitter<Entity>();

  // Translation getter
  get translations(): LanguageTranslations {
    return LANGUAGE_TRANSLATIONS[this.language];
  }

  // Get translated entities
  get translatedEntities(): Entity[] {
    return this.entities.length > 0 ? SAMPLE_ENTITIES : [];
  }

  /**
   * Handle entity selection
   */
  onEntitySelect(entity: Entity): void {
    this.entitySelected.emit(entity);
  }
}
