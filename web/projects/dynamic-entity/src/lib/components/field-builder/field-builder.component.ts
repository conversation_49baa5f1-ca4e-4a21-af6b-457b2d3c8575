import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import {
  Entity,
  FieldType,
  DynamicField,
  DynamicColumn,
  DynamicRow,
  DynamicSection
} from '../../interfaces/dynamic-entity.interfaces';
import { FIELD_TYPES } from '../../constants/field-types.constants';
import { SupportedLanguage, LanguageTranslations, LANGUAGE_TRANSLATIONS } from '../../constants/language.constants';
import { DynamicEntityUtilsService } from '../../shared/services/dynamic-entity-utils.service';

@Component({
  selector: 'lib-field-builder',
  templateUrl: './field-builder.component.html',
  styleUrls: ['./field-builder.component.scss']
})
export class FieldBuilderComponent implements AfterViewInit {
  @Input() selectedEntity: Entity | null = null;
  @Input() language: SupportedLanguage = 'en';
  @Input() sections: DynamicSection[] = [];
  @Output() sectionsChange = new EventEmitter<DynamicSection[]>();
  @Output() fieldsBuilt = new EventEmitter<DynamicSection[]>();

  @ViewChild('sectionInput') sectionInput?: ElementRef<HTMLInputElement>;

  // Field builder data
  fieldTypes: FieldType[] = FIELD_TYPES;

  constructor(private utilsService: DynamicEntityUtilsService) {}

  // Translation getter
  get translations(): LanguageTranslations {
    return LANGUAGE_TRANSLATIONS[this.language];
  }

  ngAfterViewInit(): void {
    // Focus input when editing section label
    if (this.sectionInput) {
      this.sectionInput.nativeElement.focus();
    }

    // Initialize builder if no sections exist
    if (this.sections.length === 0) {
      this.addSection();
    }
  }

  // Section management
  addSection(): void {
    const newSection: DynamicSection = {
      id: this.utilsService.generateId(),
      label: `${this.translations.addSection} ${this.sections.length + 1}`,
      rows: [],
    };

    this.sections.push(newSection);
    this.addRowToSection(newSection);
    this.emitSectionsChange();
  }

  removeSection(index: number): void {
    this.sections.splice(index, 1);
    this.emitSectionsChange();
  }

  editSectionLabel(section: DynamicSection): void {
    section.isEditing = true;
    setTimeout(() => {
      if (this.sectionInput) {
        this.sectionInput.nativeElement.focus();
        this.sectionInput.nativeElement.select();
      }
    });
  }

  saveSectionLabel(section: DynamicSection): void {
    section.isEditing = false;
    this.emitSectionsChange();
  }

  // Row management
  addRowToSection(section: DynamicSection): void {
    const newRow: DynamicRow = {
      id: this.utilsService.generateId(),
      columns: [],
    };

    section.rows.push(newRow);
    this.addColumnToRow(newRow);
    this.emitSectionsChange();
  }

  removeRowFromSection(section: DynamicSection, rowIndex: number): void {
    section.rows.splice(rowIndex, 1);
    this.emitSectionsChange();
  }

  // Column management
  addColumnToRow(row: DynamicRow): void {
    const newColumn: DynamicColumn = {
      id: this.utilsService.generateId(),
      width: Math.floor(12 / (row.columns.length + 1)),
    };

    row.columns.push(newColumn);
    this.utilsService.redistributeColumnWidths(row.columns);
    this.emitSectionsChange();
  }

  removeColumnFromRow(row: DynamicRow, columnIndex: number): void {
    row.columns.splice(columnIndex, 1);
    this.utilsService.redistributeColumnWidths(row.columns);
    this.emitSectionsChange();
  }

  // Field management
  onFieldDrop(event: CdkDragDrop<DynamicColumn[]>, column: DynamicColumn): void {
    if (event.previousContainer !== event.container) {
      const fieldType = event.item.data as FieldType;
      this.createFieldInColumn(column, fieldType);
    }
  }

  private createFieldInColumn(column: DynamicColumn, fieldType: FieldType): void {
    const translatedFieldName = this.utilsService.getTranslatedFieldType(
      fieldType.type,
      this.translations
    );
    const newField: DynamicField = {
      id: this.utilsService.generateId(),
      fieldType: fieldType,
      label: translatedFieldName,
      required: false,
      placeholder: this.utilsService.createFieldPlaceholder(translatedFieldName, this.language),
    };

    column.field = newField;
    this.emitSectionsChange();
  }

  removeFieldFromColumn(column: DynamicColumn): void {
    column.field = undefined;
    this.emitSectionsChange();
  }

  // Utility methods
  getTranslatedFieldType(fieldType: string, translations: LanguageTranslations): string {
    return this.utilsService.getTranslatedFieldType(fieldType, translations);
  }

  private emitSectionsChange(): void {
    this.sectionsChange.emit(this.sections);
  }

  onFieldsBuilt(): void {
    this.fieldsBuilt.emit(this.sections);
  }
}
