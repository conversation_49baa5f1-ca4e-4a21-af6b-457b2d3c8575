<div class="popup-overlay" *ngIf="isVisible" (click)="onOverlayClick()">
  <div class="popup-container" (click)="$event.stopPropagation()">
    <!-- Header -->
    <div class="popup-header">
      <div class="header-content">
        <button class="back-button" *ngIf="currentView === 'builder'" (click)="goBackToEntities()">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </button>
        <h2 class="popup-title">
          {{ currentView === "entities" ? translations.dynamicEntityBuilder : selectedEntity?.name + " - " + translations.fieldBuilder }}
        </h2>
      </div>
      <button class="close-button" (click)="onClose()">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </button>
    </div>

    <!-- Content Container with Slide Animation -->
    <div class="content-container">
      <!-- Entities View -->
      <ng-container *ngIf="currentView === 'entities'">
        <lib-entity-list [entities]="entities" [language]="language" (entitySelected)="onEntitySelected($event)"> </lib-entity-list>
      </ng-container>

      <!-- Field Builder View -->
      <ng-container *ngIf="currentView === 'builder'">
        <lib-field-builder [selectedEntity]="selectedEntity" [language]="language" [sections]="sections" (sectionsChange)="onSectionsChange($event)" (fieldsBuilt)="onFieldsBuilt($event)"> </lib-field-builder>
      </ng-container>
    </div>
  </div>
</div>
