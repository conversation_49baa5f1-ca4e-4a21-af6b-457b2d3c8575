import { Component, EventEmitter, Input, Output } from "@angular/core";
import {
  Entity,
  DynamicSection,
  ViewType,
} from "../interfaces/dynamic-entity.interfaces";
import { SAMPLE_ENTITIES } from "../constants/field-types.constants";
import {
  SupportedLanguage,
  LanguageTranslations,
  LANGUAGE_TRANSLATIONS,
} from "../constants/language.constants";

@Component({
  selector: "lib-dynamic-entity-popup",
  templateUrl: "./dynamic-entity-popup.component.html",
  styleUrls: ["./dynamic-entity-popup.component.scss"],
})
export class DynamicEntityPopupComponent {
  @Input() isVisible: boolean = false;
  @Input() entities: Entity[] = SAMPLE_ENTITIES;
  @Input() language: SupportedLanguage = "en";
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<void>();
  @Output() entitySelected = new EventEmitter<Entity>();
  @Output() fieldsBuilt = new EventEmitter<DynamicSection[]>();

  // Component state
  currentView: ViewType = "entities";
  selectedEntity: Entity | null = null;
  sections: DynamicSection[] = [];

  // Translation getter
  get translations(): LanguageTranslations {
    return LANGUAGE_TRANSLATIONS[this.language];
  }

  onClose(): void {
    this.close.emit();
    this.resetComponent();
  }

  onConfirm(): void {
    this.confirm.emit();
    if (this.currentView === "builder") {
      this.fieldsBuilt.emit(this.sections);
    }
  }

  onOverlayClick(): void {
    this.onClose();
  }

  onEntitySelected(entity: Entity): void {
    this.selectedEntity = entity;
    this.entitySelected.emit(entity);
    this.slideToBuilder();
  }

  goBackToEntities(): void {
    this.slideToEntities();
  }

  onSectionsChange(sections: DynamicSection[]): void {
    this.sections = sections;
  }

  onFieldsBuilt(sections: DynamicSection[]): void {
    this.sections = sections;
    this.fieldsBuilt.emit(sections);
  }

  private slideToBuilder(): void {
    this.currentView = "builder";
  }

  private slideToEntities(): void {
    this.currentView = "entities";
  }

  private resetComponent(): void {
    this.currentView = "entities";
    this.selectedEntity = null;
    this.sections = [];
  }
}
