import { Injectable } from '@angular/core';
import { LanguageTranslations } from '../../constants/language.constants';

@Injectable({
  providedIn: 'root'
})
export class DynamicEntityUtilsService {

  /**
   * Generate a unique ID for components
   */
  generateId(): string {
    return Math.random().toString(36).substring(2, 9);
  }

  /**
   * Get translated field type name
   */
  getTranslatedFieldType(fieldType: string, translations: LanguageTranslations): string {
    switch (fieldType) {
      case 'text':
        return translations.text;
      case 'number':
        return translations.number;
      case 'email':
        return translations.email;
      case 'date':
        return translations.date;
      case 'select':
        return translations.select;
      case 'textarea':
        return translations.textarea;
      case 'checkbox':
        return translations.checkbox;
      case 'file':
        return translations.file;
      case 'color':
        return translations.color;
      default:
        return fieldType;
    }
  }

  /**
   * Redistribute column widths evenly in a row
   */
  redistributeColumnWidths(columns: any[]): void {
    const equalWidth = Math.floor(12 / columns.length);
    columns.forEach((column) => {
      column.width = equalWidth;
    });
  }

  /**
   * Create field placeholder text based on language
   */
  createFieldPlaceholder(fieldLabel: string, language: string): string {
    const enterText = language === 'ar' ? 'أدخل' : 'Enter';
    return `${enterText} ${fieldLabel.toLowerCase()}`;
  }
}
